/* Estilos para el módulo de logística - Usando el mismo diseño exitoso de charts_dashboard.php */

/* Estilos para el botón de limpiar búsqueda */
.search-box {
  position: relative;
}

.btn-clear-search {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.5);
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
}

.btn-clear-search:hover {
  color: var(--accent-color);
  background: rgba(255, 255, 255, 0.1);
}

.search-input:not(:placeholder-shown) + .btn-clear-search {
  opacity: 1;
  visibility: visible;
}

.search-input {
  padding-right: 40px !important; /* Espacio para el botón de limpiar */
}

/* Variables globales */
:root {
  --primary-bg: #1e2746;
  --card-bg: #2a3353;
  --accent-color: #00e1fd;
  --accent-color-secondary: #28a745;
  --text-color: #ffffff;
  --text-secondary: #8a8eaa;
  --border-radius: 12px;
  --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --blue-gradient: linear-gradient(135deg, #0077b6 0%, #023e8a 100%);
  --cyan-gradient: linear-gradient(135deg, #00e1fd 0%, #0077b6 100%);
  --purple-gradient: linear-gradient(135deg, #8338ec 0%, #3a0ca3 100%);
  --green-gradient: linear-gradient(135deg, #2a9d8f 0%, #1b6659 100%);
  --orange-gradient: linear-gradient(135deg, #f48c06 0%, #dc2f02 100%);
}

/* Base de diseño responsivo */
body.mod-logistica {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  background-color: var(--primary-bg);
  color: var(--text-color);
  line-height: 1.5;
  overflow-x: hidden;
  width: 100%;
  position: relative;
}

body.mod-logistica .app-container {
  max-width: 100%;
  min-height: 100vh;
  padding: 15px;
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  width: 100%;
}

/* Cabecera de aplicación */
body.mod-logistica .app-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  background: var(--blue-gradient);
  padding: 15px 20px;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

body.mod-logistica h1 {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  text-align: center;
  color: var(--text-color);
}

/* Estilos para secciones de actividad */
body.mod-logistica .activity-section {
  margin-bottom: 100px;
  background: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  width: 100%;
}

body.mod-logistica .activity-header {
  margin-bottom: 0;
  background: var(--blue-gradient);
  padding: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

body.mod-logistica .activity-header .header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

body.mod-logistica .activity-header h2 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: var(--text-color);
}

body.mod-logistica .activity-metrics {
  background: var(--card-bg);
  padding: 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: block !important;
  width: 100%;
}

/* Estilos para los filtros de búsqueda, pestañas y exportación */
.search-filter-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
  width: 100%;
}

.search-box {
  width: 100%;
  
}

/* Estilos para los botones de exportación */
.export-buttons {
  display: flex;
  align-items: center;
  gap: 5px;
}

.export-buttons .action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  background: rgba(0, 225, 253, 0.1);
  border: 1px solid rgba(0, 225, 253, 0.3);
  border-radius: 5px;
  cursor: pointer;
  color: var(--accent-color);
  transition: all 0.2s ease;
}

.export-buttons .action-btn:hover {
  background: rgba(0, 225, 253, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 225, 253, 0.2);
}

/* Estilos para las secciones de tabla */
.table-section {
  margin-bottom: 30px;
  background: var(--card-bg, #2a3353);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.table-title {
  padding: 15px 20px;
  margin: 0;
  font-size: 1.2rem;
  color: #ffffff;
  background: linear-gradient(135deg, #0077b6 0%, #023e8a 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Botones de exportación para cada sección */
.section-export-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  color: white;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0;
}

.section-export-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.section-export-btn i {
  font-size: 14px;
}

/* Contenedor de tablas */
.tables-container {
  display: flex;
  flex-direction: column;
  gap: 30px;
  width: 100%;
}

/* Estilos para centrar contenido de tablas */
.data-table {
  width: 100%;
  border-collapse: collapse;
  text-align: center;
}

.data-table th,
.data-table td {
  text-align: center !important;
  vertical-align: middle !important;
  padding: 12px 8px;
}

/* Asegurar que los encabezados de las columnas estén centrados */
.data-table thead th {
  text-align: center !important;
  vertical-align: middle !important;
}

/* Asegurar que el contenido de las celdas de encabezado esté centrado */
.data-table th .sort-header {
  justify-content: center !important;
  text-align: center !important;
  margin: 0 auto;
}

/* Asegurar que los íconos de ordenación estén correctamente posicionados */
.data-table th .sort-icon {
  margin-left: 8px;
}

/* Reducir tamaño de títulos de tabla */
/* Estilos para encabezados ordenables */
.sortable {
  cursor: pointer;
  position: relative;
  user-select: none;
  transition: background-color 0.2s ease;
}

.sortable:hover {
  background-color: rgba(0, 225, 253, 0.1);
}

.sortable .sort-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
}

.sortable .sort-icon {
  font-size: 0.8em;
  color: var(--accent-color);
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.sortable:hover .sort-icon {
  opacity: 1;
}

.sort-asc .sort-icon,
.sort-desc .sort-icon {
  opacity: 1;
}

.sort-asc .sort-icon {
  transform: rotate(180deg);
}

.table-title {
  font-size: 0.85em !important; /* 15% más pequeño */
  justify-content: center !important;
  align-items: center !important;
  text-align: center;
  width: 100%;
  padding: 10px 0 !important;
}

.table-container {
  width: 100%;
  overflow-x: auto;
}

/* Ajustes para tablas específicas */
.table-container {
  display: block !important;
  opacity: 1 !important;
}

/* Estilos para el contenedor de búsqueda */
.search-box {
  margin-bottom: 15px;
}

/* Estilos para las pestañas */
.tabs-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 15px;
}

.tab-button {
  padding: 8px 15px;
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex: 1;
  min-width: 80px;
  max-width: 120px;
  text-align: center;
}

.tab-button:hover {
  background: rgba(0, 225, 253, 0.1);
  color: var(--accent-color);
  border-color: rgba(0, 225, 253, 0.3);
}

.tab-button.active {
  background: rgba(0, 225, 253, 0.2);
  color: var(--accent-color);
  border-color: var(--accent-color);
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 225, 253, 0.2);
}

/* Estilos de búsqueda y filtros */
body.mod-logistica .search-input {
  flex: 1;
  min-width: 150px;
  max-width: 100%;
  padding: 10px 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  background: rgba(0, 0, 0, 0.2);
  color: var(--text-color);
  font-size: 14px;
  width: 100%;
}

body.mod-logistica .search-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* Estilos para tabla de datos */
body.mod-logistica .table-container {
  width: 100%;
  overflow-x: auto;
  padding: 0;
  margin: 0;
}

body.mod-logistica .data-table {
  width: 100%;
  border-collapse: collapse;
  color: var(--text-color);
  margin: 0;
}

body.mod-logistica .data-table th {
  background: rgba(0, 225, 253, 0.1);
  color: var(--accent-color);
  padding: 12px 15px;
  text-align: left;
  font-weight: 600;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

body.mod-logistica .data-table th.sort-asc:after {
  content: "▲";
  font-size: 10px;
  margin-left: 5px;
  vertical-align: middle;
}

body.mod-logistica .data-table th.sort-desc:after {
  content: "▼";
  font-size: 10px;
  margin-left: 5px;
  vertical-align: middle;
}

body.mod-logistica .data-table td {
  padding: 12px 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

body.mod-logistica .data-table tr:hover {
  background: rgba(255, 255, 255, 0.03);
}

/* Distribución de columnas */
body.mod-logistica .data-table th:nth-child(1) { width: 25%; } /* Serie */
body.mod-logistica .data-table th:nth-child(2) { width: 15%; } /* Tipo */
body.mod-logistica .data-table th:nth-child(3) { width: 20%; } /* Estado */
body.mod-logistica .data-table th:nth-child(4) { width: 20%; } /* Fecha */
body.mod-logistica .data-table th:nth-child(5) { width: 20%; } /* Acciones */

/* Estilos para badges de estado */
body.mod-logistica .status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  display: inline-block;
  text-align: center;
  min-width: 70px;
}

body.mod-logistica .status-badge.disponible {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

body.mod-logistica .status-badge.pendiente {
  background: rgba(255, 152, 0, 0.2);
  color: #FF9800;
  border: 1px solid rgba(255, 152, 0, 0.3);
}

body.mod-logistica .status-badge.transito {
  background: rgba(33, 150, 243, 0.2);
  color: #2196F3;
  border: 1px solid rgba(33, 150, 243, 0.3);
}

body.mod-logistica .status-badge.faltante {
  background: rgba(244, 67, 54, 0.2);
  color: #F44336;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

body.mod-logistica .status-badge.recibido {
  background: rgba(0, 188, 212, 0.2);
  color: #00BCD4;
  border: 1px solid rgba(0, 188, 212, 0.3);
}

body.mod-logistica .status-badge.reversa {
  background: rgba(121, 85, 72, 0.2);
  color: #795548;
  border: 1px solid rgba(121, 85, 72, 0.3);
}

body.mod-logistica .status-badge.instalada {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

/* Contenedor de botones de acción */
body.mod-logistica .action-buttons-container {
  display: flex;
  gap: 4px;
  justify-content: center;
  align-items: center;
  flex-wrap: nowrap;
}

/* Botones de acción individuales */
body.mod-logistica .action-btn {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  padding: 6px 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 28px;
  height: 28px;
  position: relative;
  overflow: hidden;
}

body.mod-logistica .action-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Estilos específicos para cada tipo de botón */
body.mod-logistica .action-btn.historial-btn {
  background: rgba(255, 193, 7, 0.15);
  border-color: rgba(255, 160, 0, 0.25);
  color: #ffc107;
}

body.mod-logistica .action-btn.historial-btn:hover {
  background: rgba(255, 193, 7, 0.25);
  border-color: rgba(255, 160, 0, 0.4);
  color: #ffd54f;
}

body.mod-logistica .action-btn.declarar-btn {
  background: rgba(76, 175, 80, 0.15);
  border-color: rgba(76, 175, 80, 0.25);
  color: #4CAF50;
}

body.mod-logistica .action-btn.declarar-btn:hover {
  background: rgba(76, 175, 80, 0.25);
  border-color: rgba(76, 175, 80, 0.4);
  color: #66bb6a;
}

body.mod-logistica .action-btn.justificar-btn {
  background: rgba(255, 152, 0, 0.15);
  border-color: rgba(255, 152, 0, 0.25);
  color: #FF9800;
}

body.mod-logistica .action-btn.justificar-btn:hover {
  background: rgba(255, 152, 0, 0.25);
  border-color: rgba(255, 152, 0, 0.4);
  color: #ffb74d;
}

/* Dropdown styles removed - using direct action buttons now */

/* Estilos para el Offcanvas de Historial */
body.mod-logistica .offcanvas {
  background: var(--card-bg);
  color: var(--text-color);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
}

body.mod-logistica .offcanvas-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: var(--blue-gradient);
}

body.mod-logistica .offcanvas-body {
  background: var(--card-bg);
}

body.mod-logistica .offcanvas-body h5 {
  color: var(--text-color);
  font-weight: 600;
  margin-bottom: 20px;
  text-align: center;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

body.mod-logistica .form-label {
  color: var(--text-color);
  font-weight: 500;
  font-size: 14px;
}

body.mod-logistica .form-control {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--text-color);
  border-radius: 6px;
}

body.mod-logistica .form-control:focus {
  background: rgba(255, 255, 255, 0.08);
  border-color: var(--accent-color);
  box-shadow: 0 0 0 0.2rem rgba(0, 225, 253, 0.25);
  color: var(--text-color);
}

/* ========================================
   ESTILOS CONSOLIDADOS PARA HISTORIAL
   ======================================== */

/* Ajustes para el offcanvas de historial */
#offcanvasHistorial .offcanvas-body,
#canvaHistorial .offcanvas-body {
    background-color: #1e2746;
    padding: 15px !important;
}

/* Ajustes para el contenedor de historial */
#webHistorial {
    padding: 12px !important;
    border-radius: 8px !important;
    border: none !important;
    background-color: rgba(30, 39, 70, 0.95) !important;
    max-height: 621px !important;
    overflow-y: auto !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
    color: white !important;
}

/* Scrollbar personalizada */
#webHistorial::-webkit-scrollbar {
    width: 5px !important;
}

#webHistorial::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05) !important;
}

#webHistorial::-webkit-scrollbar-thumb {
    background: rgba(0, 225, 253, 0.5) !important;
    border-radius: 3px !important;
}

/* Selector específico para las tarjetas - Pixel-perfect como en la imagen de referencia */
#webHistorial .card-item {
    background: #1e2746 !important;
    border: 2px solid #00e1fd !important;
    border-radius: 8px !important;
    padding: 14px !important;
    margin-bottom: 16px !important;
    margin-left: -6px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4) !important;
    transition: all 0.3s ease !important;
    position: relative !important;
}

#webHistorial .card-item:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 6px 16px rgba(0, 225, 253, 0.3) !important;
    border-color: #00e1fd !important;
}

/* Selector específico para el contenido de las tarjetas */
#webHistorial .card-content {
    display: flex !important;
    flex-direction: column !important;
    gap: 0 !important;
    padding: 0 !important;
    background: transparent !important;
    border-left: none !important;
    color: white !important;
}

/* Estilos para la fecha/hora - Alineada a la izquierda */
#webHistorial .card-date,
#webHistorial span.card-date {
    background-color: transparent !important;
    color: white !important;
    border-radius: 4px !important;
    padding: 6px 10px !important;
    margin-bottom: 10px !important;
    display: flex !important;
    align-items: center !important;
    gap: 6px !important;
    font-size: 13px !important;
    font-weight: 600 !important;
    width: fit-content !important;
    text-align: left !important;
    justify-content: flex-start !important;
}

/* Estilos para las etiquetas strong dentro de los elementos */
#webHistorial .card-date strong,
#webHistorial .card-info strong {
    font-weight: 700 !important;
    color: rgba(255, 255, 255, 0.9) !important;
    margin-right: 8px !important;
}

/* Icono de calendario para la fecha */
#webHistorial .card-date::before {
    content: '📅' !important;
    font-size: 13px !important;
    margin-right: 4px !important;
}

/* Estilos para la información de la tarjeta - Mejorados para visibilidad */
#webHistorial .card-info,
p.card-info {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
    margin-bottom: 6px !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
    padding: 2px 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    color: white !important;
    font-weight: 400 !important;
    text-align: left !important;
    width: 100% !important;
}

/* Iconos específicos con emojis - Actualizado para estructura HTML correcta */
/* Estructura: .card-item > .card-content > .card-date + .card-info(s) */

/* Primer .card-info (después de .card-date) - Movimiento */
#webHistorial .card-content .card-info:nth-of-type(1)::before {
    content: '📦' !important;
    color: #00e1fd !important;
    font-size: 13px !important;
    margin-right: 6px !important;
}

/* Segundo .card-info - Desde */
#webHistorial .card-content .card-info:nth-of-type(2)::before {
    content: '📍' !important;
    color: #ff6b6b !important;
    font-size: 13px !important;
    margin-right: 6px !important;
}

/* Tercer .card-info - Hacia */
#webHistorial .card-content .card-info:nth-of-type(3)::before {
    content: '🎯' !important;
    color: #c084fc !important;
    font-size: 13px !important;
    margin-right: 6px !important;
}

/* Cuarto .card-info - Motivo/Observación */
#webHistorial .card-content .card-info:nth-of-type(4)::before {
    content: '💭' !important;
    color: #74c0fc !important;
    font-size: 13px !important;
    margin-right: 6px !important;
}

/* Quinto .card-info - Observaciones adicionales */
#webHistorial .card-content .card-info:nth-of-type(5)::before {
    content: '📝' !important;
    color: #74c0fc !important;
    font-size: 13px !important;
    margin-right: 6px !important;
}

/* Sexto .card-info - Archivos */
#webHistorial .card-content .card-info:nth-of-type(6)::before {
    content: '📎' !important;
    color: #26a69a !important;
    font-size: 13px !important;
    margin-right: 6px !important;
}

/* Optimizaciones finales para pixel-perfect match */
#webHistorial .card-info:last-child {
    margin-bottom: 0 !important;
}

/* Timeline container optimizado */
#webHistorial .timeline-container {
    min-height: 200px !important;
    max-height: 500px !important;
    overflow-y: auto !important;
    padding: 10px !important;
}

#webHistorial .timeline-container .card-item:first-child {
    margin-top: 0 !important;
}

#webHistorial .timeline-container .card-item:last-child {
    margin-bottom: 8px !important;
}

/* Estilo específico para el offcanvas de historial */
#offcanvasHistorial .offcanvas-body {
    padding: 16px !important;
}

/* Estilos para los campos de entrada */
#serieHistorial, #precioHistorial {
    background-color: rgba(0, 0, 0, 0.2) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: white !important;
}

/* Mejora para los bordes y estilo general del offcanvas */
.offcanvas-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Estilo específico para los indicadores visuales */
.form-label {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 500 !important;
}

/* Estilo para links dentro del historial */
#webHistorial a {
    color: #4fc3f7 !important;
    text-decoration: none !important;
    transition: color 0.2s !important;
}

#webHistorial a:hover {
    color: #81d4fa !important;
    text-decoration: underline !important;
}

/* Texto para coincidencia exacta con captura - Etiquetas */
#webHistorial p:has(i.bi-box)::before {
    content: "Movimiento: " !important;
    font-weight: 600 !important;
    margin-right: 4px !important;
}

#webHistorial p:has(i.bi-geo-alt)::before {
    content: "Desde: " !important;
    font-weight: 600 !important;
    margin-right: 4px !important;
}

#webHistorial p:has(i.bi-heart)::before {
    content: "Hacia: " !important;
    font-weight: 600 !important;
    margin-right: 4px !important;
}

/* Asegurar que el primer div (fecha) sea el único con ese background */
#webHistorial > div > div:first-of-type {
    background-color: transparent !important;
    display: inline-flex !important;
    align-items: center !important;
    border-radius: 4px !important;
    padding: 4px 6px !important;
    margin-bottom: 8px !important;
    font-size: 13px !important;
    font-weight: 500 !important;
}

/* Estilos específicos para elementos generados por el script historial_loader.js */
#webHistorial .movimiento-info,
#webHistorial .desde-info,
#webHistorial .hacia-info,
#webHistorial .motivo-info,
#webHistorial .obs-info,
#webHistorial .archivo-info {
    display: flex !important;
    align-items: center !important;
    margin-bottom: 8px !important;
    font-size: 14px !important;
}

#webHistorial .movimiento-info i,
#webHistorial .desde-info i,
#webHistorial .hacia-info i,
#webHistorial .motivo-info i,
#webHistorial .obs-info i,
#webHistorial .archivo-info i {
    width: 24px !important;
    margin-right: 10px !important;
    text-align: center !important;
    font-size: 16px !important;
    flex-shrink: 0 !important;
}

/* Scrollbar mejorado para el contenedor de historial */
#webHistorial::-webkit-scrollbar {
    width: 6px !important;
}

#webHistorial::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1) !important;
    border-radius: 3px !important;
}

#webHistorial::-webkit-scrollbar-thumb {
    background: #00e1fd !important;
    border-radius: 3px !important;
}

#webHistorial::-webkit-scrollbar-thumb:hover {
    background: #00c4e6 !important;
}

/* Mejoras para elementos de fecha con iconos */
#webHistorial .historial-date i {
    font-size: 16px !important;
    opacity: 0.8 !important;
}

/* Tipo de movimiento con diseño mejorado */
#webHistorial .historial-movement-type {
    background: linear-gradient(135deg, rgba(0, 225, 253, 0.2) 0%, rgba(40, 167, 69, 0.2) 100%) !important;
    color: white !important;
    padding: 4px 12px !important;
    border-radius: 20px !important;
    font-size: 12px !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    border: 1px solid rgba(0, 225, 253, 0.3) !important;
}

/* Flujo de ubicaciones mejorado */
#webHistorial .historial-location-flow {
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
    margin-bottom: 12px !important;
    padding: 12px !important;
    background: rgba(0, 0, 0, 0.2) !important;
    border-radius: 8px !important;
    border-left: 3px solid #00e1fd !important;
}

#webHistorial .location-item {
    display: flex !important;
    align-items: center !important;
    gap: 6px !important;
    flex: 1 !important;
}

#webHistorial .location-item i {
    font-size: 14px !important;
    opacity: 0.9 !important;
}

#webHistorial .location-label {
    font-size: 12px !important;
    color: rgba(255, 255, 255, 0.7) !important;
    font-weight: 500 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

#webHistorial .location-value {
    color: white !important;
    font-weight: 600 !important;
    font-size: 13px !important;
}

#webHistorial .location-arrow {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0 8px !important;
}

#webHistorial .location-arrow i {
    font-size: 16px !important;
    color: #00e1fd !important;
    animation: pulse 2s infinite !important;
}

/* Detalles adicionales del historial */
#webHistorial .historial-detail {
    display: flex !important;
    align-items: flex-start !important;
    gap: 8px !important;
    margin-bottom: 8px !important;
    padding: 8px 12px !important;
    background: rgba(255, 255, 255, 0.03) !important;
    border-radius: 6px !important;
    border-left: 2px solid rgba(0, 225, 253, 0.4) !important;
}

#webHistorial .historial-detail i {
    font-size: 14px !important;
    color: #00e1fd !important;
    margin-top: 2px !important;
    opacity: 0.8 !important;
}

#webHistorial .detail-label {
    font-size: 12px !important;
    color: rgba(255, 255, 255, 0.7) !important;
    font-weight: 500 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    min-width: 80px !important;
}

#webHistorial .detail-value {
    color: white !important;
    font-size: 13px !important;
    line-height: 1.4 !important;
    flex: 1 !important;
}

/* Pie de la tarjeta de historial */
#webHistorial .historial-footer {
    padding-top: 8px !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Botones de descarga mejorados */
#webHistorial .historial-download-btn {
    display: inline-flex !important;
    align-items: center !important;
    gap: 8px !important;
    padding: 8px 16px !important;
    background: linear-gradient(135deg, rgba(0, 225, 253, 0.1) 0%, rgba(40, 167, 69, 0.1) 100%) !important;
    border: 1px solid rgba(0, 225, 253, 0.3) !important;
    border-radius: 20px !important;
    color: #00e1fd !important;
    text-decoration: none !important;
    font-size: 12px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

#webHistorial .historial-download-btn:hover {
    background: linear-gradient(135deg, rgba(0, 225, 253, 0.2) 0%, rgba(40, 167, 69, 0.2) 100%) !important;
    border-color: #00e1fd !important;
    color: white !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0, 225, 253, 0.2) !important;
}

#webHistorial .historial-download-btn i {
    font-size: 14px !important;
}

/* =====================================
   ESTILOS PARA HEADER CON SSE STATUS
   ===================================== */

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.header-content h1 {
    margin: 0;
    flex: 1;
}

.sse-status-container {
    display: flex;
    align-items: center;
    margin-left: 20px;
}

.sse-status {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    white-space: nowrap;
}

/* Estados del SSE */
.sse-starting {
    background: rgba(108, 117, 125, 0.2);
    color: #6c757d;
    border-color: rgba(108, 117, 125, 0.3);
}

.sse-connected {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
    border-color: rgba(40, 167, 69, 0.3);
    box-shadow: 0 0 8px rgba(40, 167, 69, 0.2);
}

.sse-error {
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
    border-color: rgba(255, 193, 7, 0.3);
    animation: pulse-warning 2s infinite;
}

.sse-failed {
    background: rgba(220, 53, 69, 0.2);
    color: #dc3545;
    border-color: rgba(220, 53, 69, 0.3);
}

/* Animación para estado de error */
@keyframes pulse-warning {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.05);
    }
}

/* Responsivo para el header */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 10px;
        align-items: flex-start;
    }
    
    .sse-status-container {
        margin-left: 0;
        align-self: flex-end;
    }
    
    .sse-status {
        font-size: 11px;
        padding: 4px 8px;
    }
}

/* Indicador de nueva actividad en tablas */
.table-new-activity {
    position: relative;
}

.table-new-activity::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: #28a745;
    border-radius: 50%;
    animation: pulse-activity 1.5s infinite;
}

@keyframes pulse-activity {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.3);
    }
}

/* Mensajes de estado cuando no hay historial */
#webHistorial .alert-warning {
    background: rgba(255, 193, 7, 0.1) !important;
    border: 1px solid rgba(255, 193, 7, 0.3) !important;
    color: #ffd43b !important;
    border-radius: 8px !important;
    padding: 12px !important;
    text-align: center !important;
}

#webHistorial .alert-danger {
    background: rgba(220, 53, 69, 0.1) !important;
    border: 1px solid rgba(220, 53, 69, 0.3) !important;
    color: #ff6b6b !important;
    border-radius: 8px !important;
    padding: 12px !important;
    text-align: center !important;
}

/* Mensaje de carga */
#webHistorial .loading-message {
    text-align: center !important;
    color: #00e1fd !important;
    font-style: italic !important;
    padding: 20px !important;
}

/* Animación de pulso para elementos dinámicos */
@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

/* Mejoras para el header del offcanvas */
#offcanvasHistorial .offcanvas-header {
    background: linear-gradient(135deg, #0077b6 0%, #023e8a 100%) !important;
    color: white !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

#offcanvasHistorial .offcanvas-title {
    color: white !important;
    font-weight: 600 !important;
}

#offcanvasHistorial .btn-close {
    filter: invert(1) !important;
}

/* Mejoras para los campos de formulario en el offcanvas */
#offcanvasHistorial .form-label {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 500 !important;
    font-size: 14px !important;
    margin-bottom: 5px !important;
}

#offcanvasHistorial .form-control {
    background: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: white !important;
    border-radius: 6px !important;
    padding: 8px 12px !important;
    font-size: 14px !important;
}

#offcanvasHistorial .form-control:focus {
    background: rgba(255, 255, 255, 0.08) !important;
    border-color: #00e1fd !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 225, 253, 0.25) !important;
    color: white !important;
}

body.mod-logistica .card-date {
  font-size: 12px;
  color: var(--text-color) !important;
  font-weight: 600;
  margin-bottom: 8px;
  padding: 4px 8px;
  background: rgba(0, 225, 253, 0.2);
  border-radius: 4px;
  display: block;
  border: 1px solid rgba(0, 225, 253, 0.3);
}

/* Asegurar que las clases de Bootstrap no sobrescriban el color de la fecha */
body.mod-logistica .card-date.text-primary,
body.mod-logistica .card-date.fw-bold {
  color: var(--text-color) !important;
}

body.mod-logistica .card-info {
  font-size: 12px;
  color: var(--text-color);
  margin: 3px 0;
  line-height: 1.5;
  word-wrap: break-word;
  overflow-wrap: break-word;
  width: 100%;
  flex-shrink: 0;
}

body.mod-logistica .card-info strong {
  color: var(--accent-color);
  font-weight: 600;
}

body.mod-logistica .card-info a {
  color: var(--accent-color);
  text-decoration: none;
  font-weight: 500;
}

body.mod-logistica .card-info a:hover {
  text-decoration: underline;
}

/* Mensaje de carga para el historial */
body.mod-logistica .loading-message {
  text-align: center;
  color: var(--text-secondary);
  font-style: italic;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px dashed rgba(255, 255, 255, 0.2);
}

/* Scrollbar personalizada para el timeline */
body.mod-logistica .timeline-container::-webkit-scrollbar {
  width: 6px;
}

body.mod-logistica .timeline-container::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

body.mod-logistica .timeline-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

body.mod-logistica .timeline-container:hover::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
}

/* Utilitarios para accesibilidad */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* Sistema de texto adaptivo para móviles */
body.mod-logistica .type-short,
body.mod-logistica .date-short {
  display: none;
}

body.mod-logistica .type-full,
body.mod-logistica .date-full {
  display: inline;
}

/* Temas oscuro/claro */
body.mod-logistica.dark-theme {
  --primary-bg: #121825;
  --card-bg: #1e2746;
}

body.mod-logistica.light-theme {
  --primary-bg: #f5f7fa;
  --card-bg: #ffffff;
  --text-color: #333333;
  --text-secondary: #666666;
}

/* Navegación inferior */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 70px;
  background-color: var(--card-bg);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 0 20px;
  z-index: 1000;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.nav-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  text-decoration: none;
  cursor: pointer;
}

.nav-item i {
  font-size: 20px;
  color: var(--text-secondary);
}

.nav-item.active i {
  color: var(--accent-color);
}

.add-button {
  width: 50px;
  height: 50px;
  background-color: var(--accent-color);
  border-radius: 50%;
  margin-top: -25px;
  box-shadow: 0 4px 10px rgba(0, 225, 253, 0.3);
}

.add-button i {
  font-size: 24px;
  color: var(--primary-bg);
}

/* Estilos específicos para cada tipo de tabla */
.table-section[data-table="directa"] .table-title {
  background: var(--blue-gradient);
}

.table-section[data-table="faltante"] .table-title {
  background: var(--orange-gradient);
}

.table-section[data-table="recepcion"] .table-title {
  background: var(--green-gradient);
}

.table-section[data-table="reversa"] .table-title {
  background: var(--purple-gradient);
}

/* Scrollbar estilizada para contenedor */
body.mod-logistica .table-container::-webkit-scrollbar {
  height: 6px;
}

body.mod-logistica .table-container::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

body.mod-logistica .table-container::-webkit-scrollbar-thumb {
  background: rgba(255,255,255,0.2);
  border-radius: 3px;
}

body.mod-logistica .table-container:hover::-webkit-scrollbar-thumb {
  background: rgba(255,255,255,0.3);
}

/* Estilos para el historial - ELIMINADOS PARA EVITAR CONFLICTOS CON LOS ESTILOS PRINCIPALES */
/* Las definiciones principales están en las líneas 638-694 con el prefijo body.mod-logistica */

.offcanvas-end {
  width: 330px;
  max-width: 100%;
  background-color: var(--card-bg);
  color: var(--text-color);
}

.offcanvas-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.offcanvas-title {
  color: var(--accent-color);
}

.btn-close {
  background-color: rgba(255, 255, 255, 0.5);
}

.offcanvas-body .form-label {
  color: var(--accent-color);
  font-size: 0.9rem;
  margin-bottom: 5px;
}

.offcanvas-body .form-control {
  background-color: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--text-color);
  padding: 8px 12px;
  font-size: 0.9rem;
}

/* Estilos para el historial de movimientos - ELIMINADOS PARA EVITAR CONFLICTOS */
/* Las definiciones principales están en las líneas 638-694 con el prefijo body.mod-logistica */

.spinner-border {
    color: var(--accent-color);
}

/* Estilos para el offcanvas de historial */
.offcanvas-header {
    background: var(--blue-gradient);
    color: white;
}

.offcanvas-title {
    color: white !important;
}

.btn-close {
    filter: invert(1);
}

/* Estilos para cuando no hay datos */
.alert-warning {
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    color: #856404;
}

.alert-danger {
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.3);
    color: #721c24;
}

/* ===== ESTILOS PARA TARJETAS DE HISTORIAL - REMOVIDOS =====
 * NOTA: Todos los estilos de historial han sido consolidados en:
 * css/historial-cards-inline.css para evitar conflictos
 */

/* ========================================
   ESTILOS ADICIONALES MOVIDOS DESDE PHP
   ======================================== */

/* Variables CSS adicionales para la paleta de colores */
:root {
  --primary-color: #00e1fd;
  --primary-hover: #00c8e6;
  --primary-dark: #00a3bf;
  --bg-dark: #1a1a1a;
  --text-light: #ffffff;
  --text-muted: #6c757d;
  --border-color: #333;
}

/* Mejoras para el botón de limpiar búsqueda */
#clearSearch {
  color: var(--text-muted) !important;
  transition: all 0.2s ease !important;
}

#clearSearch:hover {
  color: var(--primary-color) !important;
  background-color: rgba(0, 225, 253, 0.1) !important;
  border-radius: 4px !important;
  transform: translateY(-50%) scale(1.1) !important;
}

/* Estilos para el input de búsqueda */
.search-input {
  background-color: #2d2d2d !important;
  border: 2px solid #444 !important;
  color: var(--text-light) !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
  font-size: 14px !important;
  transition: all 0.3s ease !important;
}

.search-input:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 225, 253, 0.25) !important;
  background-color: #333 !important;
  outline: none !important;
}

.search-input::placeholder {
  color: #999 !important;
}

/* Estilos para las pestañas */
.tabs-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
  width: 100%;
}

.tab-button {
  background: linear-gradient(145deg, #2d2d2d, #3a3a3a);
  border: 2px solid #444;
  border-radius: 8px;
  color: var(--text-light);
  cursor: pointer;
  font-weight: 600;
  padding: 12px 20px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 13px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.tab-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 225, 253, 0.2), transparent);
  transition: left 0.5s ease;
}

.tab-button:hover {
  background: linear-gradient(145deg, #3a3a3a, #4a4a4a);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 225, 253, 0.3);
}

.tab-button:hover::before {
  left: 100%;
}

.tab-button.active {
  background: linear-gradient(145deg, var(--primary-color), var(--primary-hover));
  border-color: var(--primary-color);
  color: #000;
  font-weight: 700;
  box-shadow: 0 0 20px rgba(0, 225, 253, 0.5);
}

.tab-button.active:hover {
  background: linear-gradient(145deg, var(--primary-hover), var(--primary-dark));
  transform: translateY(-1px);
}

/* Estilos para botones de acción en las tablas - Agrandados 10% y con colores iniciales */
.action-btn {
  background: linear-gradient(145deg, #2d2d2d, #3a3a3a) !important;
  border: 1px solid #555 !important;
  border-radius: 6px !important;
  color: var(--text-light) !important;
  padding: 7px 9px !important; /* 10% más grande */
  margin: 0 2px !important;
  transition: all 0.2s ease !important;
  font-size: 13px !important; /* 10% más grande */
  min-width: 35px !important; /* 10% más grande */
  height: 35px !important; /* 10% más grande */
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Botón historial - Amarillo desde el inicio */
.action-btn.historial-btn {
  color: #ffc107 !important;
  border-color: #ffc107 !important;
  background: linear-gradient(145deg, rgba(255, 193, 7, 0.1), rgba(255, 193, 7, 0.2)) !important;
  box-shadow: 0 1px 3px rgba(255, 193, 7, 0.2) !important;
}

.action-btn.historial-btn:hover {
  color: #000000 !important;
  border-color: #ffc107 !important;
  background: linear-gradient(145deg, #ffc107, #ffcd39) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 3px 8px rgba(255, 193, 7, 0.4) !important;
}

/* Botón declarar - Verde desde el inicio */
.action-btn.declarar-btn {
  color: #28a745 !important;
  border-color: #28a745 !important;
  background: linear-gradient(145deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.2)) !important;
  box-shadow: 0 1px 3px rgba(40, 167, 69, 0.2) !important;
}

.action-btn.declarar-btn:hover {
  color: #ffffff !important;
  border-color: #28a745 !important;
  background: linear-gradient(145deg, #28a745, #34ce57) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 3px 8px rgba(40, 167, 69, 0.4) !important;
}

/* Botón aceptar - Verde desde el inicio (específico para recepción) */
.action-btn.aceptar-btn {
  color: #28a745 !important;
  border-color: #28a745 !important;
  background: linear-gradient(145deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.2)) !important;
  box-shadow: 0 1px 3px rgba(40, 167, 69, 0.2) !important;
}

.action-btn.aceptar-btn:hover {
  color: #ffffff !important;
  border-color: #28a745 !important;
  background: linear-gradient(145deg, #28a745, #34ce57) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 3px 8px rgba(40, 167, 69, 0.4) !important;
}

/* Botón justificar - Rojo desde el inicio */
.action-btn.justificar-btn {
  color: #dc3545 !important;
  border-color: #dc3545 !important;
  background: linear-gradient(145deg, rgba(220, 53, 69, 0.1), rgba(220, 53, 69, 0.2)) !important;
  box-shadow: 0 1px 3px rgba(220, 53, 69, 0.2) !important;
}

.action-btn.justificar-btn:hover {
  color: #ffffff !important;
  border-color: #dc3545 !important;
  background: linear-gradient(145deg, #dc3545, #e4606d) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 3px 8px rgba(220, 53, 69, 0.4) !important;
}

/* Estilos para las tablas */
.data-table {
  background-color: #2d2d2d !important;
  border: 1px solid #444 !important;
  border-radius: 8px !important;
  overflow: hidden !important;
}

.data-table thead th {
  background: linear-gradient(145deg, var(--primary-color), var(--primary-hover)) !important;
  color: #000 !important;
  font-weight: 700 !important;
  border: none !important;
  padding: 15px 12px !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  font-size: 12px !important;
}

.data-table tbody tr {
  background-color: #333 !important;
  border-bottom: 1px solid #555 !important;
  transition: all 0.2s ease !important;
}

.data-table tbody tr:hover {
  background-color: #3a3a3a !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 8px rgba(0, 225, 253, 0.1) !important;
}

.data-table tbody td {
  color: var(--text-light) !important;
  border: none !important;
  padding: 12px !important;
  vertical-align: middle !important;
}

/* Estilos para los badges de estado */
.status-badge {
  padding: 4px 12px !important;
  border-radius: 20px !important;
  font-size: 10px !important; /* Reducido de 11px a 10px */
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

/* Estado disponible - verde con texto blanco */
.status-badge.disponible {
  background: linear-gradient(145deg, #28a745, #218838) !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3) !important;
}

/* Estados de rechazo - rojo con texto blanco */
.status-badge.rechazo {
  background: linear-gradient(145deg, #dc3545, #c82333) !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3) !important;
}

/* Estado especial Rechaza bodega - naranja oscuro con texto blanco */
.status-badge.rechaza-bodega {
  background: linear-gradient(145deg, #e67e22, #d35400) !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(230, 126, 34, 0.3) !important;
}

/* Todos los demás estados - amarillo con texto negro */
.status-badge.default {
  background: linear-gradient(145deg, #ffc107, #e0a800) !important;
  color: #000 !important;
  box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3) !important;
}

/* Clases anteriores mantenidas por compatibilidad */
.status-badge.pendiente {
  background: linear-gradient(145deg, #ffc107, #e0a800) !important;
  color: #000 !important;
  box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3) !important;
}

.status-badge.transito {
  background: linear-gradient(145deg, #ffc107, #e0a800) !important;
  color: #000 !important;
  box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3) !important;
}

.status-badge.faltante {
  background: linear-gradient(145deg, #dc3545, #c82333) !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3) !important;
}

.status-badge.reversa {
  background: linear-gradient(145deg, #ffc107, #e0a800) !important;
  color: #000 !important;
  box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3) !important;
}

.status-badge.recibido {
  background: linear-gradient(145deg, #ffc107, #e0a800) !important;
  color: #000 !important;
  box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3) !important;
}

.status-badge.instalada {
  background: linear-gradient(145deg, #28a745, #218838) !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3) !important;
}

/* Estilos para botones de reintentar y mensajes de error */
.btn-outline-primary {
  border-color: var(--primary-color) !important;
  color: var(--primary-color) !important;
  background: transparent !important;
  transition: all 0.2s ease !important;
}

.btn-outline-primary:hover {
  background: var(--primary-color) !important;
  color: #000 !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 6px rgba(0, 225, 253, 0.3) !important;
}

/* Estilos para elementos de carga y error */
.loading-container {
  color: var(--primary-color) !important;
}

/* Animaciones para filas de tabla */
@keyframes fadeOutRow {
  from {
    opacity: 1;
    transform: translateY(0);
    max-height: 100px;
  }
  to {
    opacity: 0;
    transform: translateY(-10px);
    max-height: 0;
    padding: 0;
    margin: 0;
    border: 0;
  }
}

.row-fadeout {
  animation: fadeOutRow 0.5s ease forwards;
  overflow: hidden;
}

.error-container {
  background-color: rgba(220, 53, 69, 0.1) !important;
  border: 1px solid #dc3545 !important;
  border-radius: 8px !important;
  padding: 15px !important;
  margin: 10px 0 !important;
}

.error-container i {
  color: #dc3545 !important;
}

/* Estilos para títulos de sección */
.table-title {
  color: var(--primary-color) !important;
  font-weight: 700 !important;
  text-transform: uppercase !important;
  letter-spacing: 1px !important;
  margin-bottom: 15px !important;
  text-shadow: 0 0 10px rgba(0, 225, 253, 0.3) !important;
}

/* Contenedor principal de la aplicación */
.app-container {
  background: linear-gradient(135deg, #1e2746 0%, #1e2746 100%) !important;
  min-height: 100vh !important;
}

/* Header de la aplicación */
.app-header h1 {
  color: var(--primary-color) !important;
  text-shadow: 0 0 20px rgba(0, 225, 253, 0.5) !important;
}

/* Por defecto, ocultar todas las secciones de tabla */
.table-section {
  display: none;
}

/* La tabla faltante será visible por defecto, pero sin !important para permitir override */
.table-section[data-table="faltante"] {
  display: block;
}

/* Estilos para lazy loading */
.loading-container {
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-style: italic;
}

.loading-row {
  background-color: #f8f9fa;
}

.table-loading {
  opacity: 0.6;
  pointer-events: none;
}

.tab-button.loading {
  position: relative;
  pointer-events: none;
}

.tab-button.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 8px;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  border: 2px solid #ffffff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* ========================================
   MODAL DE RECHAZO - ESTILOS TEMÁTICOS
   ======================================== */

.modal-rechazo-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background: rgba(30, 39, 70, 0.9) !important; /* Usando el color primario con transparencia */
  backdrop-filter: blur(4px);
  z-index: 1050 !important;
  align-items: center !important;
  justify-content: center !important;
  animation: fadeInOverlay 0.3s ease;
}

/* Solo mostrar cuando tenga la clase visible o el estilo inline flex */
.modal-rechazo-overlay.show,
.modal-rechazo-overlay[style*="flex"] {
  display: flex !important;
}

.modal-rechazo-content {
  background: var(--card-bg) !important; /* #2a3353 */
  border-radius: 12px !important;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 
              0 0 0 1px rgba(0, 225, 253, 0.2) !important; /* Borde sutil accent color */
  max-width: 420px !important;
  width: 90% !important;
  max-height: 90vh !important;
  overflow: hidden !important;
  animation: slideInModal 0.3s ease;
}

.modal-rechazo-header {
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.2), rgba(220, 53, 69, 0.1)) !important;
  border-bottom: 1px solid rgba(220, 53, 69, 0.3) !important;
  padding: 20px 24px !important;
}

.modal-rechazo-title {
  color: #dc3545 !important;
  font-size: 1.25rem !important;
  font-weight: 600 !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
}

.modal-rechazo-title i {
  color: #dc3545 !important;
  font-size: 1.1rem !important;
}

.modal-rechazo-body {
  padding: 24px !important;
}

.form-group-rechazo {
  margin-bottom: 0 !important;
}

.form-label-rechazo {
  display: flex !important;
  align-items: center !important;
  color: var(--text-color) !important; /* #ffffff */
  font-size: 0.95rem !important;
  font-weight: 500 !important;
  margin-bottom: 12px !important;
}

.form-label-rechazo i {
  color: var(--accent-color) !important; /* #00e1fd */
  font-size: 0.9rem !important;
}

.form-select-rechazo {
  width: 100% !important;
  padding: 12px 16px !important;
  background: var(--primary-bg) !important; /* #1e2746 */
  border: 2px solid rgba(0, 225, 253, 0.3) !important;
  border-radius: 8px !important;
  color: var(--text-color) !important;
  font-size: 0.95rem !important;
  transition: all 0.3s ease !important;
  outline: none !important;
}

.form-select-rechazo:focus {
  border-color: var(--accent-color) !important;
  box-shadow: 0 0 0 3px rgba(0, 225, 253, 0.1) !important;
}

.form-select-rechazo option {
  background: var(--primary-bg) !important;
  color: var(--text-color) !important;
  padding: 8px !important;
}

.modal-rechazo-footer {
  background: rgba(30, 39, 70, 0.5) !important;
  border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
  padding: 20px 24px !important;
  display: flex !important;
  gap: 12px !important;
  justify-content: flex-end !important;
}

.btn-rechazo-confirm,
.btn-rechazo-cancel {
  padding: 12px 20px !important;
  border: none !important;
  border-radius: 8px !important;
  font-size: 0.9rem !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  transition: all 0.3s ease !important;
  outline: none !important;
}

.btn-rechazo-confirm {
  background: linear-gradient(135deg, #dc3545, #c82333) !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3) !important;
}

.btn-rechazo-confirm:hover {
  background: linear-gradient(135deg, #c82333, #bd2130) !important;
  box-shadow: 0 6px 16px rgba(220, 53, 69, 0.4) !important;
  transform: translateY(-1px) !important;
}

.btn-rechazo-cancel {
  background: rgba(108, 117, 125, 0.2) !important;
  color: var(--text-secondary) !important; /* #8a8eaa */
  border: 1px solid rgba(108, 117, 125, 0.3) !important;
}

.btn-rechazo-cancel:hover {
  background: rgba(108, 117, 125, 0.3) !important;
  color: var(--text-color) !important;
  border-color: rgba(108, 117, 125, 0.5) !important;
  transform: translateY(-1px) !important;
}

/* Animaciones del modal */
@keyframes fadeInOverlay {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInModal {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Responsive para móviles */
@media (max-width: 480px) {
  .modal-rechazo-content {
    margin: 20px !important;
    width: calc(100% - 40px) !important;
  }
  
  .modal-rechazo-header,
  .modal-rechazo-body,
  .modal-rechazo-footer {
    padding: 16px !important;
  }
  
  .modal-rechazo-footer {
    flex-direction: column !important;
  }
  
  .btn-rechazo-confirm,
  .btn-rechazo-cancel {
    width: 100% !important;
    justify-content: center !important;
  }
}
